; Complete VirtualBox Hardware Configuration Script
; With Anti-Detection Features (VBoxManage Only)
; Generated from DMI data extraction
; Change VM variable below to your VM name

VM := "YourVMName"
vboxman := "C:\Program Files\Oracle\VirtualBox\VBoxManage.exe"

; ============= DMI/SMBIOS INFORMATION =============

; BIOS Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "American Megatrends Inc.",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "F12",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "01/16/2019",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "13",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0",,hide

; System Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Not Specified",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "H370HD3",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "Not Specified",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "03D502E0-045E-0587-4906-A30700080009",,hide

; Board Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Gigabyte Technology Co., Ltd.",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "H370 HD3-CF",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "x.x",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" "Power Supply Fan",,hide

; Chassis Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "To Be Filled By O.E.M.",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" "Switching",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Default string",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "To Be Filled By O.E.M.",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "To Be Filled By O.E.M.",,hide

; Processor Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer" "Intel(R) Corporation",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion" "Intel(R) Core(TM) i5-8600 CPU @ 3.10GHz",,hide

; OEM Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A",,hide

; ============= STORAGE DEVICES =============

; Primary Hard Drive (SATA/AHCI)
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "ST2000LX001-1RG174",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "1.0",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "            WDZBXEA9",,hide

; Primary Hard Drive (IDE - Alternative)
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "ST2000LX001-1RG174",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "1.0",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "            WDZBXEA9",,hide

; ============= ACPI CONFIGURATION =============

; ACPI Information
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "GBT",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorId" "MSFT",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorRev" "0x01000013",,hide

; ============= ANTI-DETECTION CONFIGURATION =============

; CPU Configuration - Hide Hypervisor Features
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/80000001/ecx" "0x00000201",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/00000001/ecx" "0x0",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/eax" "0x00000000",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/ebx" "0x00000000",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/ecx" "0x00000000",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/edx" "0x00000000",,hide

; Memory and Timing Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled" "1",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/TM/TSCMode" "RealTSCOffset",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/TM/TSCTiedToExecution" "1",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/TM/TSCNotTiedToHalt" "1",,hide

; PCI Configuration - Hide VirtualBox Signatures
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomDeviceId" "0x1237",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomSubSystemVendorId" "0x1af4",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomSubSystemId" "0x1100",,hide

; Graphics Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomVendorId" "0x10de",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomDeviceId" "0x0a65",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomSubSystemVendorId" "0x10de",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomSubSystemId" "0x0000",,hide

; Audio Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomDeviceId" "0x293e",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomSubSystemId" "0x80860101",,hide

; Network Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomDeviceId" "0x10d3",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomSubSystemVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomSubSystemId" "0x0001",,hide

; IDE/SATA Controller Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/CustomDeviceId" "0x7010",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/CustomDeviceId" "0x2829",,hide

; USB Configuration
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/usb-ohci/0/Config/CustomVendorId" "0x106b",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/usb-ohci/0/Config/CustomDeviceId" "0x003f",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/usb-ehci/0/Config/CustomVendorId" "0x8086",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/usb-ehci/0/Config/CustomDeviceId" "0x293a",,hide

; Additional Anti-Detection Measures
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/PDM/HaltOnReset" "1",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/HWVIRTEX/Exclusive" "1",,hide
; Disable VirtualBox-specific devices
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/TestingEnabled" "0",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/TestingMMIO" "0",,hide

; Hide hypervisor vendor
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/HypervisorPresent" "0",,hide
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/CPUM/MSRs/IntelNetBurst/Present" "0",,hide

; CPU Vendor masking (Intel)
RunWait, %vboxman% modifyvm "%VM%" --cpuid-set 00000000 0000000d 756e6547 6c65746e 49656e69,,hide
RunWait, %vboxman% modifyvm "%VM%" --cpuid-set 80000000 80000008 00000000 00000000 00000000,,hide
RunWait, %vboxman% modifyvm "%VM%" --cpuid-set 80000001 00000000 00000000 00000001 20100800,,hide

; Remove VirtualBox ACPI signature
RunWait, %vboxman% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/SLICTable" "",,hide

MsgBox, Complete hardware fingerprint with anti-detection applied to VM: %VM%

Hardware Detection Summary:
- Hard Disk: Detected (ST2000LX001-1RG174)
- DVD Drive: Not Detected
ExitApp