@echo off
REM Complete VirtualBox Hardware Configuration Script
REM With Anti-Detection Features (VBoxManage Only)
REM Generated from DMI data extraction
REM Change VM variable below to your VM name

set VM=YourVMName
set VBOXMAN="C:\Program Files\Oracle\VirtualBox\VBoxManage.exe"

echo ============= DMI/SMBIOS INFORMATION =============

echo BIOS Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "American Megatrends Inc."
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "F12"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "01/16/2019"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "13"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

echo System Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "Not Specified"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "H370HD3"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "Not Specified"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "03D502E0-045E-0587-4906-A30700080009"

echo Board Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "Gigabyte Technology Co., Ltd."
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "H370 HD3-CF"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "x.x"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" "Power Supply Fan"

echo Chassis Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "To Be Filled By O.E.M."
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" "Switching"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "Default string"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "To Be Filled By O.E.M."
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "To Be Filled By O.E.M."

echo Processor Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer" "Intel(R) Corporation"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion" "Intel(R) Core(TM) i5-8600 CPU @ 3.10GHz"

echo OEM Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

echo ============= STORAGE DEVICES =============

echo Primary Hard Drive (SATA/AHCI)
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "ST2000LX001-1RG174"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "1.0"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "            WDZBXEA9"

echo Primary Hard Drive (IDE - Alternative)
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber" "ST2000LX001-1RG174"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision" "1.0"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber" "            WDZBXEA9"

echo ============= ACPI CONFIGURATION =============

echo ACPI Information
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "GBT"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorId" "MSFT"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorRev" "0x01000013"

echo ============= ANTI-DETECTION CONFIGURATION =============

echo CPU Configuration - Hide Hypervisor Features
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/80000001/ecx" "0x00000201"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/00000001/ecx" "0x0"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/eax" "0x00000000"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/ebx" "0x00000000"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/ecx" "0x00000000"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HostCPUID/40000000/edx" "0x00000000"

echo Memory and Timing Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled" "1"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/TM/TSCMode" "RealTSCOffset"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/TM/TSCTiedToExecution" "1"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/TM/TSCNotTiedToHalt" "1"

echo PCI Configuration - Hide VirtualBox Signatures
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomDeviceId" "0x1237"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomSubSystemVendorId" "0x1af4"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/pci/0/Config/CustomSubSystemId" "0x1100"

echo Graphics Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomVendorId" "0x10de"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomDeviceId" "0x0a65"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomSubSystemVendorId" "0x10de"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/vga/0/Config/CustomSubSystemId" "0x0000"

echo Audio Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomDeviceId" "0x293e"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/hda/0/Config/CustomSubSystemId" "0x80860101"

echo Network Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomDeviceId" "0x10d3"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomSubSystemVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/e1000/0/Config/CustomSubSystemId" "0x0001"

echo IDE/SATA Controller Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/piix3ide/0/Config/CustomDeviceId" "0x7010"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/ahci/0/Config/CustomDeviceId" "0x2829"

echo USB Configuration
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/usb-ohci/0/Config/CustomVendorId" "0x106b"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/usb-ohci/0/Config/CustomDeviceId" "0x003f"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/usb-ehci/0/Config/CustomVendorId" "0x8086"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/usb-ehci/0/Config/CustomDeviceId" "0x293a"

echo Additional Anti-Detection Measures
%VBOXMAN% setextradata "%VM%" "VBoxInternal/PDM/HaltOnReset" "1"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/HWVIRTEX/Exclusive" "1"
echo Disable VirtualBox-specific devices
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/TestingEnabled" "0"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/VMMDev/0/Config/TestingMMIO" "0"

echo Hide hypervisor vendor
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/HypervisorPresent" "0"
%VBOXMAN% setextradata "%VM%" "VBoxInternal/CPUM/MSRs/IntelNetBurst/Present" "0"

echo CPU Vendor masking (Intel)
%VBOXMAN% modifyvm "%VM%" --cpuid-set 00000000 0000000d 756e6547 6c65746e 49656e69
%VBOXMAN% modifyvm "%VM%" --cpuid-set 80000000 80000008 00000000 00000000 00000000
%VBOXMAN% modifyvm "%VM%" --cpuid-set 80000001 00000000 00000000 00000001 20100800

echo Remove VirtualBox ACPI signature
%VBOXMAN% setextradata "%VM%" "VBoxInternal/Devices/acpi/0/Config/SLICTable" ""

echo.
echo Complete hardware fingerprint with anti-detection applied to VM: %VM%
echo.
echo Hardware Detection Summary:
echo - Hard Disk: Detected (ST2000LX001-1RG174)
echo - DVD Drive: Not Detected
pause