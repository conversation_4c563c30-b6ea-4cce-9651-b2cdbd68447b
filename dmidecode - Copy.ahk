﻿#NoEnv
#SingleInstance Force

; Run dmidecode.exe and capture output to dmidecode.txt
RunWait, %comspec% /c dmidecode.exe > dmidecode.txt, , Hide
if ErrorLevel {
    MsgBox, 16, Error, Failed to run dmidecode.exe. Please ensure:`n`n1. dmidecode.exe is in the same folder as this script`n2. You are running as Administrator`n3. dmidecode.exe is compatible with your system
    ExitApp
}

; Verify the file was created and has content
FileGetSize, fileSize, dmidecode.txt
if (ErrorLevel || fileSize = 0) {
    MsgBox, 16, Error, dmidecode.txt was not created or is empty.`nPlease check if dmidecode.exe ran successfully.
    ExitApp
}

; Read the generated dmidecode.txt file
FileRead, content, dmidecode.txt
if ErrorLevel {
    MsgBox, 16, Error, Error reading generated dmidecode.txt file
    ExitApp
}


; Initialize all DMI variables
DmiBIOSVendor := ""
DmiBIOSVersion := ""
DmiBIOSReleaseDate := ""
DmiBIOSReleaseMajor := ""
DmiBIOSReleaseMinor := ""
DmiBIOSFirmwareMajor := "1"
DmiBIOSFirmwareMinor := "0"

DmiSystemVendor := ""
DmiSystemProduct := ""
DmiSystemVersion := ""
DmiSystemSerial := ""
DmiSystemSKU := ""
DmiSystemFamily := ""
DmiSystemUuid := ""

DmiBoardVendor := ""
DmiBoardProduct := ""
DmiBoardVersion := ""
DmiBoardSerial := ""
DmiBoardAssetTag := ""
DmiBoardLocInChass := ""
DmiBoardBoardType := ""

DmiChassisVendor := ""
DmiChassisType := ""
DmiChassisVersion := ""
DmiChassisSerial := ""
DmiChassisAssetTag := ""

DmiProcManufacturer := ""
DmiProcVersion := ""

; Storage device variables
ModelNumber := ""
FirmwareRevision := ""
SerialNumber := ""
CDModelNumber := ""
CDFirmwareRevision := ""
CDSerialNumber := ""
ATAPIVendorId := ""
ATAPIProductId := ""
ATAPIRevision := ""

; Detection flags
HasHardDisk := false
HasDVDDrive := false

; ACPI variables
AcpiOemId := ""
AcpiCreatorId := "MSFT"
AcpiCreatorRev := "0x01000013"

DmiOEMVBoxVer := "Extended version info: 1.00.00"
DmiOEMVBoxRev := "Extended revision info: 1A"

; Parse dmidecode.txt content line by line
Lines := StrSplit(content, "`n", "`r")
CurrentSection := ""
i := 1

while (i <= Lines.Length()) {
    line := Trim(Lines[i])
    
    ; Detect DMI sections
    if (InStr(line, "Handle") && InStr(line, "DMI type 0")) {
        CurrentSection := "BIOS"
        i++
        continue
    }
    else if (InStr(line, "Handle") && InStr(line, "DMI type 1")) {
        CurrentSection := "System"
        i++
        continue
    }
    else if (InStr(line, "Handle") && InStr(line, "DMI type 2")) {
        CurrentSection := "Board"
        i++
        continue
    }
    else if (InStr(line, "Handle") && InStr(line, "DMI type 3")) {
        CurrentSection := "Chassis"
        i++
        continue
    }
    else if (InStr(line, "Handle") && InStr(line, "DMI type 4")) {
        CurrentSection := "Processor"
        i++
        continue
    }
    else if (InStr(line, "Handle") && InStr(line, "DMI type") && !InStr(line, "type 0") && !InStr(line, "type 1") && !InStr(line, "type 2") && !InStr(line, "type 3") && !InStr(line, "type 4")) {
        CurrentSection := ""
    }
    
    ; Parse BIOS Information (Type 0)
    if (CurrentSection = "BIOS") {
        if (InStr(line, "Vendor:")) {
            DmiBIOSVendor := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiBIOSVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Release Date:")) {
            DmiBIOSReleaseDate := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "BIOS Revision:")) {
            revision := Trim(SubStr(line, InStr(line, ":") + 1))
            if (InStr(revision, ".")) {
                parts := StrSplit(revision, ".")
                DmiBIOSReleaseMajor := parts[1]
                DmiBIOSReleaseMinor := parts[2]
            } else {
                DmiBIOSReleaseMajor := revision
                DmiBIOSReleaseMinor := "0"
            }
        }
    }
    
    ; Parse System Information (Type 1)
    else if (CurrentSection = "System") {
        if (InStr(line, "Manufacturer:")) {
            DmiSystemVendor := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Product Name:")) {
            DmiSystemProduct := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiSystemVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Serial Number:")) {
            DmiSystemSerial := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "UUID:")) {
            DmiSystemUuid := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "SKU Number:")) {
            DmiSystemSKU := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Family:")) {
            DmiSystemFamily := Trim(SubStr(line, InStr(line, ":") + 1))
        }
    }
    
    ; Parse Base Board Information (Type 2)
    else if (CurrentSection = "Board") {
        if (InStr(line, "Manufacturer:")) {
            DmiBoardVendor := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Product Name:")) {
            DmiBoardProduct := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiBoardVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Serial Number:")) {
            DmiBoardSerial := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Asset Tag:")) {
            DmiBoardAssetTag := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Location In Chassis:")) {
            DmiBoardLocInChass := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Type:")) {
            boardType := Trim(SubStr(line, InStr(line, ":") + 1))
            if (boardType = "Motherboard") {
                DmiBoardBoardType := "10"
            } else {
                DmiBoardBoardType := boardType
            }
        }
    }
    
    ; Parse Chassis Information (Type 3)
    else if (CurrentSection = "Chassis") {
        if (InStr(line, "Manufacturer:")) {
            DmiChassisVendor := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Type:")) {
            chassisType := Trim(SubStr(line, InStr(line, ":") + 1))
            if (chassisType = "Desktop") {
                DmiChassisType := "3"
            } else if (chassisType = "Laptop") {
                DmiChassisType := "9"
            } else if (chassisType = "Notebook") {
                DmiChassisType := "10"
            } else {
                DmiChassisType := chassisType
            }
        }
        else if (InStr(line, "Version:")) {
            DmiChassisVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Serial Number:")) {
            DmiChassisSerial := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Asset Tag:")) {
            DmiChassisAssetTag := Trim(SubStr(line, InStr(line, ":") + 1))
        }
    }
    
    ; Parse Processor Information (Type 4)
    else if (CurrentSection = "Processor") {
        if (InStr(line, "Manufacturer:")) {
            DmiProcManufacturer := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiProcVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
    }
    
    i++
}

; Get additional hardware info via WMI
try {
    ; Get storage device info (first physical drive)
    for objItem in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'") {
        if (A_Index = 1) {  ; First drive
            ModelNumber := objItem.Model
            SerialNumber := objItem.SerialNumber
            FirmwareRevision := "1.0"  ; Default if not available
            
            ; Check if we actually got valid data
            if (ModelNumber != "" && ModelNumber != "Not Specified" && SerialNumber != "" && SerialNumber != "Not Specified") {
                HasHardDisk := true
            }
        }
        break
    }
    
    ; Get CD/DVD drive info
    for objItem in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_CDROMDrive") {
        if (A_Index = 1) {  ; First drive
            CDModelNumber := objItem.Name
            CDSerialNumber := objItem.DeviceID
            CDFirmwareRevision := "1.0"
            
            ; Check if we actually got valid data
            if (CDModelNumber != "" && CDModelNumber != "Not Specified") {
                HasDVDDrive := true
                
                ; Extract vendor and product from name
                if (InStr(CDModelNumber, " ")) {
                    parts := StrSplit(CDModelNumber, " ", , 2)
                    ATAPIVendorId := parts[1]
                    ATAPIProductId := parts[2]
                } else {
                    ATAPIVendorId := "Generic"
                    ATAPIProductId := CDModelNumber
                }
                ATAPIRevision := CDFirmwareRevision
            }
        }
        break
    }
    
    ; Fallback: Get system info if dmidecode failed
    if (DmiSystemVendor = "" || DmiSystemVendor = "Default string") {
        for objItem in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_ComputerSystem") {
            if (DmiSystemVendor = "" || DmiSystemVendor = "Default string") {
                DmiSystemVendor := objItem.Manufacturer
            }
            if (DmiSystemProduct = "" || DmiSystemProduct = "Default string") {
                DmiSystemProduct := objItem.Model
            }
            break
        }
    }
    
    ; Get BIOS info if missing
    if (DmiBIOSVendor = "") {
        for objItem in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_BIOS") {
            DmiBIOSVendor := objItem.Manufacturer
            DmiBIOSVersion := objItem.SMBIOSBIOSVersion
            DmiBIOSReleaseDate := objItem.ReleaseDate
            break
        }
    }
    
} catch e {
    ; WMI failed - no storage devices detected
    HasHardDisk := false
    HasDVDDrive := false
}

; Try to get ACPI OEM ID from registry
try {
    RegRead, AcpiOemId, HKEY_LOCAL_MACHINE\HARDWARE\ACPI\DSDT, OEM ID
} catch {
    ; Default based on system vendor
    if (InStr(DmiSystemVendor, "Gigabyte") || InStr(DmiBoardVendor, "Gigabyte")) {
        AcpiOemId := "GBT"
    } else if (InStr(DmiSystemVendor, "ASUS") || InStr(DmiBoardVendor, "ASUS")) {
        AcpiOemId := "ASUS"
    } else if (InStr(DmiSystemVendor, "Dell")) {
        AcpiOemId := "DELL"
    } else if (InStr(DmiSystemVendor, "HP")) {
        AcpiOemId := "HP"
    } else {
        AcpiOemId := "ACPI"
    }
}

; Build INI output with proper VirtualBox-mapped variable names
output := "; VirtualBox Hardware Configuration Mapping`n"
output .= "; Generated from DMI data extraction`n`n"

output .= "[PCBIOS_DMI_CONFIG]`n"
output .= "pcbios_DmiBIOSVendor=" . DmiBIOSVendor . "`n"
output .= "pcbios_DmiBIOSVersion=" . DmiBIOSVersion . "`n"
output .= "pcbios_DmiBIOSReleaseDate=" . DmiBIOSReleaseDate . "`n"
output .= "pcbios_DmiBIOSReleaseMajor=" . DmiBIOSReleaseMajor . "`n"
output .= "pcbios_DmiBIOSReleaseMinor=" . DmiBIOSReleaseMinor . "`n"
output .= "pcbios_DmiBIOSFirmwareMajor=" . DmiBIOSFirmwareMajor . "`n"
output .= "pcbios_DmiBIOSFirmwareMinor=" . DmiBIOSFirmwareMinor . "`n"
output .= "`n"

output .= "pcbios_DmiSystemVendor=" . DmiSystemVendor . "`n"
output .= "pcbios_DmiSystemProduct=" . DmiSystemProduct . "`n"
output .= "pcbios_DmiSystemVersion=" . DmiSystemVersion . "`n"
output .= "pcbios_DmiSystemSerial=" . DmiSystemSerial . "`n"
output .= "pcbios_DmiSystemSKU=" . DmiSystemSKU . "`n"
output .= "pcbios_DmiSystemFamily=" . DmiSystemFamily . "`n"
output .= "pcbios_DmiSystemUuid=" . DmiSystemUuid . "`n"
output .= "`n"

output .= "pcbios_DmiBoardVendor=" . DmiBoardVendor . "`n"
output .= "pcbios_DmiBoardProduct=" . DmiBoardProduct . "`n"
output .= "pcbios_DmiBoardVersion=" . DmiBoardVersion . "`n"
output .= "pcbios_DmiBoardSerial=" . DmiBoardSerial . "`n"
output .= "pcbios_DmiBoardAssetTag=" . DmiBoardAssetTag . "`n"
output .= "pcbios_DmiBoardLocInChass=" . DmiBoardLocInChass . "`n"
output .= "pcbios_DmiBoardBoardType=" . DmiBoardBoardType . "`n"
output .= "`n"

output .= "pcbios_DmiChassisVendor=" . DmiChassisVendor . "`n"
output .= "pcbios_DmiChassisType=" . DmiChassisType . "`n"
output .= "pcbios_DmiChassisVersion=" . DmiChassisVersion . "`n"
output .= "pcbios_DmiChassisSerial=" . DmiChassisSerial . "`n"
output .= "pcbios_DmiChassisAssetTag=" . DmiChassisAssetTag . "`n"
output .= "`n"

output .= "pcbios_DmiProcManufacturer=" . DmiProcManufacturer . "`n"
output .= "pcbios_DmiProcVersion=" . DmiProcVersion . "`n"
output .= "`n"

output .= "pcbios_DmiOEMVBoxVer=" . DmiOEMVBoxVer . "`n"
output .= "pcbios_DmiOEMVBoxRev=" . DmiOEMVBoxRev . "`n"
output .= "`n"

output .= "[STORAGE_CONFIG]`n"
; Only include storage info if detected
if (HasHardDisk) {
    output .= "ahci_port0_ModelNumber=" . ModelNumber . "`n"
    output .= "ahci_port0_FirmwareRevision=" . FirmwareRevision . "`n"
    output .= "ahci_port0_SerialNumber=" . SerialNumber . "`n"
    output .= "piix3ide_primarymaster_ModelNumber=" . ModelNumber . "`n"
    output .= "piix3ide_primarymaster_FirmwareRevision=" . FirmwareRevision . "`n"
    output .= "piix3ide_primarymaster_SerialNumber=" . SerialNumber . "`n"
} else {
    output .= "ahci_port0_ModelNumber=Not Detected`n"
    output .= "ahci_port0_FirmwareRevision=Not Detected`n"
    output .= "ahci_port0_SerialNumber=Not Detected`n"
    output .= "piix3ide_primarymaster_ModelNumber=Not Detected`n"
    output .= "piix3ide_primarymaster_FirmwareRevision=Not Detected`n"
    output .= "piix3ide_primarymaster_SerialNumber=Not Detected`n"
}

if (HasDVDDrive) {
    output .= "piix3ide_primaryslave_ModelNumber=" . CDModelNumber . "`n"
    output .= "piix3ide_primaryslave_FirmwareRevision=" . CDFirmwareRevision . "`n"
    output .= "piix3ide_primaryslave_SerialNumber=" . CDSerialNumber . "`n"
    output .= "piix3ide_primaryslave_ATAPIVendorId=" . ATAPIVendorId . "`n"
    output .= "piix3ide_primaryslave_ATAPIProductId=" . ATAPIProductId . "`n"
    output .= "piix3ide_primaryslave_ATAPIRevision=" . ATAPIRevision . "`n"
} else {
    output .= "piix3ide_primaryslave_ModelNumber=Not Detected`n"
    output .= "piix3ide_primaryslave_FirmwareRevision=Not Detected`n"
    output .= "piix3ide_primaryslave_SerialNumber=Not Detected`n"
    output .= "piix3ide_primaryslave_ATAPIVendorId=Not Detected`n"
    output .= "piix3ide_primaryslave_ATAPIProductId=Not Detected`n"
    output .= "piix3ide_primaryslave_ATAPIRevision=Not Detected`n"
}

output .= "`n"
output .= "[ACPI_CONFIG]`n"
output .= "acpi_AcpiOemId=" . AcpiOemId . "`n"
output .= "acpi_AcpiCreatorId=" . AcpiCreatorId . "`n"
output .= "acpi_AcpiCreatorRev=" . AcpiCreatorRev . "`n"
output .= "`n"

output .= "[DETECTION_STATUS]`n"
output .= "HasHardDisk=" . (HasHardDisk ? "Yes" : "No") . "`n"
output .= "HasDVDDrive=" . (HasDVDDrive ? "Yes" : "No") . "`n"

; Build complete VirtualBox AHK script (unchanged)
vboxScript := "; Complete VirtualBox Hardware Configuration Script`n"
vboxScript .= "; With Anti-Detection Features (VBoxManage Only)`n"
vboxScript .= "; Generated from DMI data extraction`n"
vboxScript .= "; Change VM variable below to your VM name`n`n"

vboxScript .= "VM := ""YourVMName""`n"
vboxScript .= "vboxman := ""C:\Program Files\Oracle\VirtualBox\VBoxManage.exe""`n`n"

vboxScript .= "; ============= DMI/SMBIOS INFORMATION =============`n`n"

vboxScript .= "; BIOS Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor"" """ . DmiBIOSVendor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion"" """ . DmiBIOSVersion . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate"" """ . DmiBIOSReleaseDate . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor"" """ . DmiBIOSReleaseMajor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor"" """ . DmiBIOSReleaseMinor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor"" """ . DmiBIOSFirmwareMajor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor"" """ . DmiBIOSFirmwareMinor . """,,hide`n`n"

vboxScript .= "; System Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor"" """ . DmiSystemVendor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct"" """ . DmiSystemProduct . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion"" """ . DmiSystemVersion . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial"" """ . DmiSystemSerial . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU"" """ . DmiSystemSKU . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily"" """ . DmiSystemFamily . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid"" """ . DmiSystemUuid . """,,hide`n`n"

vboxScript .= "; Board Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor"" """ . DmiBoardVendor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct"" """ . DmiBoardProduct . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion"" """ . DmiBoardVersion . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial"" """ . DmiBoardSerial . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag"" """ . DmiBoardAssetTag . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass"" """ . DmiBoardLocInChass . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType"" """ . DmiBoardBoardType . """,,hide`n`n"

vboxScript .= "; Chassis Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor"" """ . DmiChassisVendor . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisType"" """ . DmiChassisType . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion"" """ . DmiChassisVersion . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial"" """ . DmiChassisSerial . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag"" """ . DmiChassisAssetTag . """,,hide`n`n"

vboxScript .= "; Processor Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer"" """ . DmiProcManufacturer . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion"" """ . DmiProcVersion . """,,hide`n`n"

vboxScript .= "; OEM Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer"" """ . DmiOEMVBoxVer . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev"" """ . DmiOEMVBoxRev . """,,hide`n`n"

; Only add storage configuration if devices were detected
if (HasHardDisk || HasDVDDrive) {
    vboxScript .= "; ============= STORAGE DEVICES =============`n`n"
    
    if (HasHardDisk) {
        vboxScript .= "; Primary Hard Drive (SATA/AHCI)`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber"" """ . ModelNumber . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision"" """ . FirmwareRevision . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber"" """ . SerialNumber . """,,hide`n`n"
        
        vboxScript .= "; Primary Hard Drive (IDE - Alternative)`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber"" """ . ModelNumber . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision"" """ . FirmwareRevision . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber"" """ . SerialNumber . """,,hide`n`n"
    }
    
    if (HasDVDDrive) {
        vboxScript .= "; CD/DVD Drive`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ModelNumber"" """ . CDModelNumber . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/FirmwareRevision"" """ . CDFirmwareRevision . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/SerialNumber"" """ . CDSerialNumber . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIVendorId"" """ . ATAPIVendorId . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIProductId"" """ . ATAPIProductId . """,,hide`n"
        vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIRevision"" """ . ATAPIRevision . """,,hide`n`n"
    }
} else {
    vboxScript .= "; ============= STORAGE DEVICES =============`n"
    vboxScript .= "; No storage devices detected - skipping storage configuration`n`n"
}

vboxScript .= "; ============= ACPI CONFIGURATION =============`n`n"

vboxScript .= "; ACPI Information`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiOemId"" """ . AcpiOemId . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiCreatorId"" """ . AcpiCreatorId . """,,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiCreatorRev"" """ . AcpiCreatorRev . """,,hide`n`n"

vboxScript .= "; ============= ANTI-DETECTION CONFIGURATION =============`n`n"

vboxScript .= "; CPU Configuration - Hide Hypervisor Features`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/80000001/ecx"" ""0x00000201"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/00000001/ecx"" ""0x0"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/eax"" ""0x00000000"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/ebx"" ""0x00000000"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/ecx"" ""0x00000000"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/edx"" ""0x00000000"",,hide`n`n"

vboxScript .= "; Memory and Timing Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled"" ""1"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/TM/TSCMode"" ""RealTSCOffset"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/TM/TSCTiedToExecution"" ""1"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/TM/TSCNotTiedToHalt"" ""1"",,hide`n`n"

vboxScript .= "; PCI Configuration - Hide VirtualBox Signatures`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomDeviceId"" ""0x1237"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomSubSystemVendorId"" ""0x1af4"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomSubSystemId"" ""0x1100"",,hide`n`n"

vboxScript .= "; Graphics Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomVendorId"" ""0x10de"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomDeviceId"" ""0x0a65"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomSubSystemVendorId"" ""0x10de"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomSubSystemId"" ""0x0000"",,hide`n`n"

vboxScript .= "; Audio Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomDeviceId"" ""0x293e"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomSubSystemId"" ""0x80860101"",,hide`n`n"

vboxScript .= "; Network Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomDeviceId"" ""0x10d3"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomSubSystemVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomSubSystemId"" ""0x0001"",,hide`n`n"

vboxScript .= "; IDE/SATA Controller Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/CustomDeviceId"" ""0x7010"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/CustomDeviceId"" ""0x2829"",,hide`n`n"

vboxScript .= "; USB Configuration`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ohci/0/Config/CustomVendorId"" ""0x106b"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ohci/0/Config/CustomDeviceId"" ""0x003f"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ehci/0/Config/CustomVendorId"" ""0x8086"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ehci/0/Config/CustomDeviceId"" ""0x293a"",,hide`n`n"

vboxScript .= "; Additional Anti-Detection Measures`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/PDM/HaltOnReset"" ""1"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/HWVIRTEX/Exclusive"" ""1"",,hide`n"

vboxScript .= "; Disable VirtualBox-specific devices`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/TestingEnabled"" ""0"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/TestingMMIO"" ""0"",,hide`n`n"

vboxScript .= "; Hide hypervisor vendor`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/HypervisorPresent"" ""0"",,hide`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/CPUM/MSRs/IntelNetBurst/Present"" ""0"",,hide`n`n"

vboxScript .= "; CPU Vendor masking (Intel)`n"
vboxScript .= "RunWait, %vboxman% modifyvm ""%VM%"" --cpuid-set 00000000 0000000d 756e6547 6c65746e 49656e69,,hide`n"
vboxScript .= "RunWait, %vboxman% modifyvm ""%VM%"" --cpuid-set 80000000 80000008 00000000 00000000 00000000,,hide`n"
vboxScript .= "RunWait, %vboxman% modifyvm ""%VM%"" --cpuid-set 80000001 00000000 00000000 00000001 20100800,,hide`n`n"

vboxScript .= "; Remove VirtualBox ACPI signature`n"
vboxScript .= "RunWait, %vboxman% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/SLICTable"" """",,hide`n"

; Build summary message
summaryMsg := "Complete hardware fingerprint with anti-detection applied to VM: %VM%`n`n"
summaryMsg .= "Hardware Detection Summary:`n"
summaryMsg .= "- Hard Disk: " . (HasHardDisk ? "Detected (" . ModelNumber . ")" : "Not Detected") . "`n"
summaryMsg .= "- DVD Drive: " . (HasDVDDrive ? "Detected (" . CDModelNumber . ")" : "Not Detected") . "`n"

vboxScript .= "`nMsgBox, " . summaryMsg
vboxScript .= "ExitApp"

; Build CMD/BAT script
cmdScript := "@echo off`n"
cmdScript .= "REM Complete VirtualBox Hardware Configuration Script`n"
cmdScript .= "REM With Anti-Detection Features (VBoxManage Only)`n"
cmdScript .= "REM Generated from DMI data extraction`n"
cmdScript .= "REM Change VM variable below to your VM name`n`n"

cmdScript .= "set VM=YourVMName`n"
cmdScript .= "set VBOXMAN=""C:\Program Files\Oracle\VirtualBox\VBoxManage.exe""`n`n"

cmdScript .= "echo ============= DMI/SMBIOS INFORMATION =============`n`n"

cmdScript .= "echo BIOS Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor"" """ . DmiBIOSVendor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion"" """ . DmiBIOSVersion . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate"" """ . DmiBIOSReleaseDate . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor"" """ . DmiBIOSReleaseMajor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor"" """ . DmiBIOSReleaseMinor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor"" """ . DmiBIOSFirmwareMajor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor"" """ . DmiBIOSFirmwareMinor . """`n`n"

cmdScript .= "echo System Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor"" """ . DmiSystemVendor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct"" """ . DmiSystemProduct . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion"" """ . DmiSystemVersion . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial"" """ . DmiSystemSerial . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU"" """ . DmiSystemSKU . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily"" """ . DmiSystemFamily . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid"" """ . DmiSystemUuid . """`n`n"

cmdScript .= "echo Board Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor"" """ . DmiBoardVendor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct"" """ . DmiBoardProduct . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion"" """ . DmiBoardVersion . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial"" """ . DmiBoardSerial . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag"" """ . DmiBoardAssetTag . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass"" """ . DmiBoardLocInChass . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType"" """ . DmiBoardBoardType . """`n`n"

cmdScript .= "echo Chassis Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor"" """ . DmiChassisVendor . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisType"" """ . DmiChassisType . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion"" """ . DmiChassisVersion . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial"" """ . DmiChassisSerial . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag"" """ . DmiChassisAssetTag . """`n`n"

cmdScript .= "echo Processor Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer"" """ . DmiProcManufacturer . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion"" """ . DmiProcVersion . """`n`n"

cmdScript .= "echo OEM Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer"" """ . DmiOEMVBoxVer . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev"" """ . DmiOEMVBoxRev . """`n`n"

; Only add storage configuration if devices were detected
if (HasHardDisk || HasDVDDrive) {
    cmdScript .= "echo ============= STORAGE DEVICES =============`n`n"
    
    if (HasHardDisk) {
        cmdScript .= "echo Primary Hard Drive (SATA/AHCI)`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber"" """ . ModelNumber . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision"" """ . FirmwareRevision . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber"" """ . SerialNumber . """`n`n"
        
        cmdScript .= "echo Primary Hard Drive (IDE - Alternative)`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/ModelNumber"" """ . ModelNumber . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/FirmwareRevision"" """ . FirmwareRevision . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimaryMaster/SerialNumber"" """ . SerialNumber . """`n`n"
    }
    
    if (HasDVDDrive) {
        cmdScript .= "echo CD/DVD Drive`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ModelNumber"" """ . CDModelNumber . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/FirmwareRevision"" """ . CDFirmwareRevision . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/SerialNumber"" """ . CDSerialNumber . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIVendorId"" """ . ATAPIVendorId . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIProductId"" """ . ATAPIProductId . """`n"
        cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/PrimarySlave/ATAPIRevision"" """ . ATAPIRevision . """`n`n"
    }
} else {
    cmdScript .= "echo ============= STORAGE DEVICES =============`n"
    cmdScript .= "echo No storage devices detected - skipping storage configuration`n`n"
}

cmdScript .= "echo ============= ACPI CONFIGURATION =============`n`n"

cmdScript .= "echo ACPI Information`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiOemId"" """ . AcpiOemId . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiCreatorId"" """ . AcpiCreatorId . """`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/AcpiCreatorRev"" """ . AcpiCreatorRev . """`n`n"

cmdScript .= "echo ============= ANTI-DETECTION CONFIGURATION =============`n`n"

cmdScript .= "echo CPU Configuration - Hide Hypervisor Features`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/80000001/ecx"" ""0x00000201""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/00000001/ecx"" ""0x0""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/eax"" ""0x00000000""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/ebx"" ""0x00000000""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/ecx"" ""0x00000000""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HostCPUID/40000000/edx"" ""0x00000000""`n`n"

cmdScript .= "echo Memory and Timing Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled"" ""1""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/TM/TSCMode"" ""RealTSCOffset""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/TM/TSCTiedToExecution"" ""1""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/TM/TSCNotTiedToHalt"" ""1""`n`n"

cmdScript .= "echo PCI Configuration - Hide VirtualBox Signatures`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomDeviceId"" ""0x1237""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomSubSystemVendorId"" ""0x1af4""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/pci/0/Config/CustomSubSystemId"" ""0x1100""`n`n"

cmdScript .= "echo Graphics Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomVendorId"" ""0x10de""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomDeviceId"" ""0x0a65""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomSubSystemVendorId"" ""0x10de""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/vga/0/Config/CustomSubSystemId"" ""0x0000""`n`n"

cmdScript .= "echo Audio Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomDeviceId"" ""0x293e""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/hda/0/Config/CustomSubSystemId"" ""0x80860101""`n`n"

cmdScript .= "echo Network Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomDeviceId"" ""0x10d3""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomSubSystemVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/e1000/0/Config/CustomSubSystemId"" ""0x0001""`n`n"

cmdScript .= "echo IDE/SATA Controller Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/piix3ide/0/Config/CustomDeviceId"" ""0x7010""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/ahci/0/Config/CustomDeviceId"" ""0x2829""`n`n"

cmdScript .= "echo USB Configuration`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ohci/0/Config/CustomVendorId"" ""0x106b""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ohci/0/Config/CustomDeviceId"" ""0x003f""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ehci/0/Config/CustomVendorId"" ""0x8086""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/usb-ehci/0/Config/CustomDeviceId"" ""0x293a""`n`n"

cmdScript .= "echo Additional Anti-Detection Measures`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/PDM/HaltOnReset"" ""1""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/HWVIRTEX/Exclusive"" ""1""`n"

cmdScript .= "echo Disable VirtualBox-specific devices`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/TestingEnabled"" ""0""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/VMMDev/0/Config/TestingMMIO"" ""0""`n`n"

cmdScript .= "echo Hide hypervisor vendor`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/HypervisorPresent"" ""0""`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/CPUM/MSRs/IntelNetBurst/Present"" ""0""`n`n"

cmdScript .= "echo CPU Vendor masking (Intel)`n"
cmdScript .= "%VBOXMAN% modifyvm ""%VM%"" --cpuid-set 00000000 0000000d 756e6547 6c65746e 49656e69`n"
cmdScript .= "%VBOXMAN% modifyvm ""%VM%"" --cpuid-set 80000000 80000008 00000000 00000000 00000000`n"
cmdScript .= "%VBOXMAN% modifyvm ""%VM%"" --cpuid-set 80000001 00000000 00000000 00000001 20100800`n`n"

cmdScript .= "echo Remove VirtualBox ACPI signature`n"
cmdScript .= "%VBOXMAN% setextradata ""%VM%"" ""VBoxInternal/Devices/acpi/0/Config/SLICTable"" """"`n`n"

cmdScript .= "echo.`n"
cmdScript .= "echo Complete hardware fingerprint with anti-detection applied to VM: %VM%`n"
cmdScript .= "echo.`n"
cmdScript .= "echo Hardware Detection Summary:`n"
cmdScript .= "echo - Hard Disk: " . (HasHardDisk ? "Detected (" . ModelNumber . ")" : "Not Detected") . "`n"
cmdScript .= "echo - DVD Drive: " . (HasDVDDrive ? "Detected (" . CDModelNumber . ")" : "Not Detected") . "`n"
cmdScript .= "pause"

; Write output files
FileDelete, dmidecode.ini
FileAppend, %output%, dmidecode.ini

FileDelete, apply_to_vbox.ahk
FileAppend, %vboxScript%, apply_to_vbox.ahk

FileDelete, apply_to_vbox.cmd
FileAppend, %cmdScript%, apply_to_vbox.cmd

if ErrorLevel {
    MsgBox, Error writing output files
} else {
    successMsg := "Complete hardware information extracted successfully!`n`n"
    successMsg .= "Files created:`n"
    successMsg .= "- dmidecode.ini (hardware data with VirtualBox-mapped variable names)`n"
    successMsg .= "- apply_to_vbox.ahk (AutoHotkey configuration script)`n"
    successMsg .= "- apply_to_vbox.cmd (Batch file configuration script)`n`n"
    successMsg .= "Hardware Detection Summary:`n"
    successMsg .= "- Hard Disk: " . (HasHardDisk ? "Detected (" . ModelNumber . ")" : "Not Detected") . "`n"
    successMsg .= "- DVD Drive: " . (HasDVDDrive ? "Detected (" . CDModelNumber . ")" : "Not Detected") . "`n`n"
    successMsg .= "Edit the VM name in either script and run it to apply the configuration."
    
    MsgBox, 64, Success, %successMsg%
}

ExitApp